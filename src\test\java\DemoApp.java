/**
 * 演示版本 - 不依赖JSqlParser，展示SQL分析器的功能
 */
public class DemoApp {
    public static void main(String[] args) {
        System.out.println("=== SQL分析器演示 ===");
        System.out.println();
        
        // 创建分析结果对象
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // 模拟分析复杂SQL的结果
        demonstrateComplexSqlAnalysis(result);
        
        // 打印分析结果
        result.printResults();
        
        System.out.println("=== 演示完成 ===");
    }
    
    /**
     * 演示复杂SQL分析的结果
     * 模拟你提供的那个复杂SQL语句的分析结果
     */
    private static void demonstrateComplexSqlAnalysis(SqlAnalysisResult result) {
        System.out.println("模拟分析复杂SQL语句...");
        System.out.println("原SQL包含：外层SELECT + 内层UNION ALL + 多个子查询");
        System.out.println();
        
        // 第一个内层查询：T_D_AI_STOCK A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query1 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT... FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE..."
        );
        query1.addTable("T_D_AI_STOCK");
        query1.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query1.addJoinColumn("A.C_DAI_CODE");
        query1.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query1.addFilterColumn("a.c_year_month");
        query1.addFilterColumn("A.C_PORT_CODE");
        query1.addFilterColumn("A.D_STOCK");
        query1.addFilterColumn("A.C_PORT_CLS_CODE");
        query1.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query1);
        
        // 第二个内层查询：T_D_AI_ACT_VAL A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query2 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY... FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE..."
        );
        query2.addTable("T_D_AI_ACT_VAL");
        query2.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query2.addJoinColumn("A.C_DAI_CODE");
        query2.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query2.addFilterColumn("A.C_PORT_CODE");
        query2.addFilterColumn("A.D_CHK_ACC");
        query2.addFilterColumn("A.N_CHECK_STATE");
        query2.addFilterColumn("A.C_PORT_CLS_CODE");
        query2.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query2);
        
        // 第三个内层查询：R_D_AI_ACT_VAL A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query3 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY... FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE..."
        );
        query3.addTable("R_D_AI_ACT_VAL");
        query3.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query3.addJoinColumn("A.C_DAI_CODE");
        query3.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query3.addFilterColumn("A.C_PORT_CODE");
        query3.addFilterColumn("A.D_CHK_ACC");
        query3.addFilterColumn("A.N_USEVAT");
        query3.addFilterColumn("A.C_PORT_CLS_CODE");
        query3.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query3);
        
        // 子查询：T_S_VAT_MKTFILTER (出现在多个NOT IN中)
        SqlAnalysisResult.QueryInfo subQuery = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER"
        );
        subQuery.addTable("T_S_VAT_MKTFILTER");
        // 这个子查询没有WHERE条件，所以没有过滤字段和关联字段
        
        result.addQueryInfo(subQuery);
        
        // 演示相同模式的查询会被分组
        SqlAnalysisResult.QueryInfo duplicateSubQuery = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER (第二次出现)"
        );
        duplicateSubQuery.addTable("T_S_VAT_MKTFILTER");
        
        result.addQueryInfo(duplicateSubQuery);
        
        System.out.println("模拟分析完成，发现 " + result.getAllQueries().size() + " 个查询");
        System.out.println();
    }
}
