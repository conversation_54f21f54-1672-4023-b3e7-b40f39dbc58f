import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * SQL文件处理器测试
 */
public class SqlFileProcessorTest {
    public static void main(String[] args) {
        System.out.println("=== SQL文件处理器测试 ===");
        
        try {
            // 创建一个测试SQL文件，模拟你的实际需求
            createTestSqlFile();
            
            // 处理测试文件
            TableFieldAnalysisResult result = SqlFileProcessor.processSqlFile("test_sqls.txt");
            
            // 打印结果
            result.printResults();
            
            System.out.println("=== 使用说明 ===");
            System.out.println("1. 准备你的SQL文件，确保：");
            System.out.println("   - 每个SQL语句以分号(;)结尾");
            System.out.println("   - 每个SQL语句后换行");
            System.out.println("   - 支持多行SQL和注释");
            System.out.println();
            System.out.println("2. 运行分析：");
            System.out.println("   mvn exec:java -Dexec.args=\"your_file.sql\"");
            System.out.println();
            System.out.println("3. 输出格式：");
            System.out.println("   表名：关联字段[...],过滤字段[...],所有字段[...]");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void createTestSqlFile() throws IOException {
        String testContent = 
            "-- 测试SQL文件\n" +
            "-- 模拟复杂的业务查询\n" +
            "\n" +
            "-- 查询1：用户订单统计\n" +
            "SELECT u.user_id, u.username, COUNT(o.order_id) as order_count\n" +
            "FROM users u\n" +
            "LEFT JOIN orders o ON u.user_id = o.user_id\n" +
            "WHERE u.status = 'active' AND u.created_date >= '2024-01-01'\n" +
            "GROUP BY u.user_id, u.username;\n" +
            "\n" +
            "-- 查询2：产品销售分析\n" +
            "SELECT p.product_id, p.product_name, SUM(oi.quantity) as total_sold\n" +
            "FROM products p\n" +
            "JOIN order_items oi ON p.product_id = oi.product_id\n" +
            "JOIN orders o ON oi.order_id = o.order_id\n" +
            "WHERE o.order_date >= '2024-01-01' AND o.status = 'completed'\n" +
            "GROUP BY p.product_id, p.product_name;\n" +
            "\n" +
            "-- 查询3：复杂的嵌套查询\n" +
            "SELECT *\n" +
            "FROM customers c\n" +
            "WHERE c.customer_id IN (\n" +
            "    SELECT DISTINCT o.customer_id\n" +
            "    FROM orders o\n" +
            "    WHERE o.total_amount > (\n" +
            "        SELECT AVG(total_amount)\n" +
            "        FROM orders\n" +
            "        WHERE order_date >= '2024-01-01'\n" +
            "    )\n" +
            ");\n" +
            "\n" +
            "-- 查询4：UNION查询示例\n" +
            "SELECT 'high_value' as customer_type, customer_id, total_spent\n" +
            "FROM (\n" +
            "    SELECT customer_id, SUM(total_amount) as total_spent\n" +
            "    FROM orders\n" +
            "    WHERE order_date >= '2024-01-01'\n" +
            "    GROUP BY customer_id\n" +
            "    HAVING SUM(total_amount) > 1000\n" +
            ") high_customers\n" +
            "UNION ALL\n" +
            "SELECT 'regular' as customer_type, customer_id, total_spent\n" +
            "FROM (\n" +
            "    SELECT customer_id, SUM(total_amount) as total_spent\n" +
            "    FROM orders\n" +
            "    WHERE order_date >= '2024-01-01'\n" +
            "    GROUP BY customer_id\n" +
            "    HAVING SUM(total_amount) <= 1000\n" +
            ") regular_customers;\n";
        
        Files.write(Paths.get("test_sqls.txt"), testContent.getBytes("UTF-8"));
        System.out.println("已创建测试SQL文件: test_sqls.txt");
        System.out.println();
    }
}
