import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

/**
 * ComprehensiveSqlAnalyzer 测试类
 * 使用复杂的实际业务SQL进行测试
 */
public class ComprehensiveSqlAnalyzerTest {
    
    public static void main(String[] args) {
        System.out.println("=== ComprehensiveSqlAnalyzer 复杂SQL测试 ===");
        System.out.println("测试目标：验证复杂业务SQL的解析能力");
        System.out.println();
        
        try {
            // 复杂的实际业务SQL（简化版，移除参数占位符以便测试）
            String complexSql = "SELECT sec.c_dv_pi_mod as C_DV_PI_MOD, A.C_IDEN AS ID, A.C_SEC_CODE, " +
                "NVL(SEC.C_SEC_VAR_CODE,'DEFAULT') AS C_SEC_VAR_CODE, A.C_DC_CODE, NVL(SEC.C_MKT_CODE,'DEFAULT') AS C_MKT_CODE, " +
                "A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, A.C_DTA_CODE, A.C_SETT_MODE, A.C_TD_CHAN_CODE, " +
                "A.C_DV_VAR_DUR, A.C_DT_CODE, A.N_TD_AMOUNT, A.N_TD_MONEY, A.N_TD_PRICE, A.N_INCOME, " +
                "A.N_SETT_MONEY_FIRST, A.N_SETT_MONEY_DUE, SEC.C_DV_PI_MOD, A.C_DESC, " +
                "CASE WHEN NVL(A.N_SETT_MONEY_FACT,0) = 0 AND NVL(A.N_PAWN_MONEY,0) = 0 " +
                "THEN A.N_SETT_MONEY_DUE ELSE A.N_SETT_MONEY_FACT END AS N_SETT_MONEY_FACT, " +
                "A.C_CA_CODE_SETT_DUE AS C_CA_CODE, A.D_TRADE, A.D_SETT_FIRST, A.D_SETT_DUE, " +
                "A.D_SETT_FACT, A.N_INTEREST, A.C_TD_NO, A.N_RATE, A.N_TD_MONEY_PORT, " +
                "STOCK.C_DV_ACCOUNT_CODE, A.N_PAWN_MONEY, " +
                "CASE WHEN NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'BOND%' THEN NVL(trim(SEC.C_DV_AI_MOD),'DEFAULT') ELSE 'OTHER' END AS C_SEC_CODE_TAG, " +
                "CASE WHEN A.C_DT_CODE IN ('BUY','SELL','TRANSFER') THEN A.C_DT_CODE ELSE 'OTHER' END AS C_DV_TYPE_SUB, " +
                "A.D_EXCHANGE_RATE, NVL(A.C_DEVALUE_STAGE,'NORMAL') AS C_DEVALUE_STAGE, " +
                "NVL(A.C_DEVALUE_STAGE,'NORMAL') AS C_FEE_CODE, A.C_DV_PLAT, 'CASH_FLOW' as C_CASH_FLOW_CODE, " +
                "CASE WHEN NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'STOCK%' AND NVL(SEC.C_FINANCE_TOOL,'') = 'EQUITY' THEN 'STOCK_TOOL' " +
                "WHEN (NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'BOND%' OR NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'FUND%' " +
                "OR NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'DERIVATIVE%') AND NVL(SEC.C_FINANCE_TOOL,'') = 'FIXED' THEN 'FIXED_TOOL' " +
                "ELSE NVL(SEC.C_FINANCE_TOOL,'DEFAULT') END AS C_FINANCE_TOOL, " +
                "A.N_PAWN_MONEY, A.C_SH_ACC_CODE, A.C_ORG_CODE, A.C_SETT_WAY, A.C_PORT_CODE, " +
                "A.N_MONEY_GZ, A.C_PAY_PAT_CODE, A.C_SETT_WAY, A.N_SETT_MONEY_DUE_FACT, " +
                "A.C_BUSI_TYPE, A.N_UNPAID_INT, A.C_DV_BCK, A.N_COMP_AMT " +
                "FROM T_D_AC_TRADE_IVT A " +
                "LEFT JOIN T_P_SV_SEC_BASE SEC ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = 1 " +
                "LEFT JOIN T_D_MP_PRE_STOCK STOCK ON A.C_SEC_CODE = STOCK.C_SEC_CODE " +
                "and '2024-01-01' between STOCK.d_ai_begin and STOCK.d_ai_end AND STOCK.N_CHECK_STATE = 1 " +
                "WHERE A.C_PORT_CODE = 'PORT001' AND A.C_DT_CODE = 'BUY' AND A.C_TD_TYPE = 'NORMAL' " +
                "AND NVL(A.C_YCDZ_STATE,'NORMAL') <> 'CANCELLED' AND A.C_DV_INVEST_CLS = 'EQUITY' " +
                "AND A.N_CHECK_STATE = 1 AND A.D_TRADE = '2024-01-01' " +
                "ORDER BY D_TRADE,C_TD_NO,C_TD_CHAN_CODE,C_SH_ACC_CODE ASC";

            System.out.println("🔍 开始解析复杂SQL...");
            System.out.println("SQL长度: " + complexSql.length() + " 字符");
            System.out.println();

            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(complexSql);
            System.out.println("✅ SQL解析成功");

            if (statement instanceof Select) {
                Select select = (Select) statement;
                
                // 创建分析器
                ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();
                
                System.out.println("🔬 开始综合分析...");
                select.accept(analyzer, null);
                
                // 获取分析结果
                SqlAnalysisResult result = analyzer.getAnalysisResult();
                
                System.out.println("✅ 分析完成");
                System.out.println();
                
                // 打印详细分析结果
                System.out.println("=== 详细分析结果 ===");
                result.printResults();
                
                // 转换为按表组织的结果
                System.out.println("\n=== 按表组织的结果 ===");
                TableFieldAnalysisResult tableResult = TableFieldAnalysisResult.fromSqlAnalysisResult(result);
                tableResult.printResults();
                
                // 转换为聚合结果
                System.out.println("\n=== 聚合统计结果 ===");
                AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(tableResult);
                aggregatedResult.printResults();
                
                // 验证分析结果
                validateAnalysisResult(result);
                
            } else {
                System.out.println("❌ 不是SELECT语句");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误:");
            e.printStackTrace();
            
            System.out.println("\n🔧 可能的问题:");
            System.out.println("1. SQL语法复杂度超出解析器能力");
            System.out.println("2. 特殊函数或语法不被支持");
            System.out.println("3. 参数占位符处理问题");
        }
    }
    
    /**
     * 验证分析结果的正确性
     */
    private static void validateAnalysisResult(SqlAnalysisResult result) {
        System.out.println("\n=== 结果验证 ===");
        
        // 验证表数量
        int expectedTables = 3; // T_D_AC_TRADE_IVT, T_P_SV_SEC_BASE, T_D_MP_PRE_STOCK
        System.out.println("📊 预期表数量: " + expectedTables);
        
        // 验证是否识别了主要的表
        boolean foundMainTable = false;
        boolean foundSecTable = false;
        boolean foundStockTable = false;
        
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            for (String table : queryInfo.getTables()) {
                if (table.toUpperCase().contains("T_D_AC_TRADE_IVT")) {
                    foundMainTable = true;
                }
                if (table.toUpperCase().contains("T_P_SV_SEC_BASE")) {
                    foundSecTable = true;
                }
                if (table.toUpperCase().contains("T_D_MP_PRE_STOCK")) {
                    foundStockTable = true;
                }
            }
        }
        
        System.out.println("✅ 主表识别: " + (foundMainTable ? "成功" : "失败"));
        System.out.println("✅ SEC表识别: " + (foundSecTable ? "成功" : "失败"));
        System.out.println("✅ STOCK表识别: " + (foundStockTable ? "成功" : "失败"));
        
        // 验证是否识别了关联条件
        boolean foundJoinConditions = false;
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            if (!queryInfo.getJoinColumns().isEmpty()) {
                foundJoinConditions = true;
                break;
            }
        }
        System.out.println("✅ 关联条件识别: " + (foundJoinConditions ? "成功" : "失败"));
        
        // 验证是否识别了过滤条件
        boolean foundFilterConditions = false;
        for (SqlAnalysisResult.QueryInfo queryInfo : result.getAllQueries()) {
            if (!queryInfo.getFilterColumns().isEmpty()) {
                foundFilterConditions = true;
                break;
            }
        }
        System.out.println("✅ 过滤条件识别: " + (foundFilterConditions ? "成功" : "失败"));
        
        System.out.println("\n🎯 测试总结:");
        System.out.println("- 复杂SQL解析能力验证完成");
        System.out.println("- LEFT JOIN语法支持验证");
        System.out.println("- 多表关联分析验证");
        System.out.println("- 复杂WHERE条件解析验证");
        System.out.println("- CASE WHEN表达式处理验证");
    }
}
