-- 演示SQL文件
-- 每个SQL以分号结尾，支持多行和注释

-- SQL 1: 简单查询
SELECT u.id, u.name 
FROM users u 
WHERE u.status = 'active';

-- SQL 2: 关联查询
SELECT a.user_id, b.order_id
FROM users a, orders b
WHERE a.id = b.user_id AND a.created_date > '2024-01-01';

-- SQL 3: 子查询
SELECT * FROM products
WHERE category_id IN (
    SELECT id FROM categories
    WHERE active = 1
);

-- SQL 4: UNION查询
SELECT customer_id, amount FROM sales WHERE date >= '2024-01-01'
UNION ALL
SELECT customer_id, amount FROM refunds WHERE date >= '2024-01-01';
