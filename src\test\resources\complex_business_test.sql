-- 复杂业务SQL测试文件
-- 基于实际业务场景的复杂查询

SELECT sec.c_dv_pi_mod as C_DV_PI_MOD, 
       A.C_IDEN AS ID, 
       A.C_SEC_CODE, 
       NVL(SEC.C_SEC_VAR_CODE,'DEFAULT') AS C_SEC_VAR_CODE, 
       A.C_DC_CODE, 
       NVL(SEC.C_MKT_CODE,'DEFAULT') AS C_MKT_CODE, 
       A.C_DV_INVEST_CLS, 
       A.C_DV_ISSUE_MODE, 
       A.C_DTA_CODE, 
       A.C_SETT_MODE,  
       A.C_TD_CHAN_CODE, 
       A.C_DV_VAR_DUR, 
       A.C_DT_CODE, 
       A.N_TD_AMOUNT, 
       A.N_TD_MONEY, 
       A.N_TD_PRICE, 
       A.N_INCOME,  
       A.N_SETT_MONEY_FIRST, 
       A.N_SETT_MONEY_DUE,
       SEC.C_DV_PI_MOD,
       A.C_DESC, 
       CASE WHEN NVL(A.N_SETT_MONEY_FACT,0) = 0 AND NVL(A.N_PAWN_MONEY,0) = 0 
            THEN A.N_SETT_MONEY_DUE 
            ELSE A.N_SETT_MONEY_FACT 
       END AS N_SETT_MONEY_FACT,  
       A.C_CA_CODE_SETT_DUE AS C_CA_CODE,
       A.D_TRADE,  
       A.D_SETT_FIRST, 
       A.D_SETT_DUE,
       A.D_SETT_FACT,
       A.N_INTEREST,
       A.C_TD_NO,
       A.N_RATE, 
       A.N_TD_MONEY_PORT, 
       STOCK.C_DV_ACCOUNT_CODE,  
       A.N_PAWN_MONEY, 
       CASE WHEN NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'BOND%'  
            THEN NVL(trim(SEC.C_DV_AI_MOD),'DEFAULT')  
            ELSE 'OTHER' 
       END AS C_SEC_CODE_TAG,  
       CASE WHEN A.C_DT_CODE IN ('BUY','SELL','TRANSFER')  
            THEN A.C_DT_CODE 
            ELSE 'OTHER' 
       END AS C_DV_TYPE_SUB, 
       A.D_EXCHANGE_RATE,
       NVL(A.C_DEVALUE_STAGE,'NORMAL') AS C_DEVALUE_STAGE, 
       NVL(A.C_DEVALUE_STAGE,'NORMAL') AS C_FEE_CODE,  
       A.C_DV_PLAT,  
       'CASH_FLOW' as C_CASH_FLOW_CODE,  
       CASE WHEN NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'STOCK%' AND NVL(SEC.C_FINANCE_TOOL,'') = 'EQUITY'  
            THEN 'STOCK_TOOL'  
            WHEN (NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'BOND%' OR NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'FUND%' 
                  OR NVL(SEC.C_SEC_VAR_CODE,'') LIKE 'DERIVATIVE%') AND NVL(SEC.C_FINANCE_TOOL,'') = 'FIXED'  
            THEN 'FIXED_TOOL'  
            ELSE NVL(SEC.C_FINANCE_TOOL,'DEFAULT') 
       END AS C_FINANCE_TOOL,
       A.N_PAWN_MONEY,
       A.C_SH_ACC_CODE,
       A.C_ORG_CODE,
       A.C_SETT_WAY,
       A.C_PORT_CODE,
       A.N_MONEY_GZ,
       A.C_PAY_PAT_CODE,
       A.C_SETT_WAY,
       A.N_SETT_MONEY_DUE_FACT,
       A.C_BUSI_TYPE,
       A.N_UNPAID_INT,
       A.C_DV_BCK,
       A.N_COMP_AMT 
FROM T_D_AC_TRADE_IVT A 
LEFT JOIN T_P_SV_SEC_BASE SEC 
    ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = 1  
LEFT JOIN T_D_MP_PRE_STOCK STOCK  
    ON A.C_SEC_CODE = STOCK.C_SEC_CODE 
    AND '2024-01-01' between STOCK.d_ai_begin and STOCK.d_ai_end 
    AND STOCK.N_CHECK_STATE = 1  
WHERE A.C_PORT_CODE = 'PORT001' 
    AND A.C_DT_CODE = 'BUY' 
    AND A.C_TD_TYPE = 'NORMAL'    
    AND NVL(A.C_YCDZ_STATE,'NORMAL') <> 'CANCELLED'    
    AND A.C_DV_INVEST_CLS = 'EQUITY' 
    AND A.N_CHECK_STATE = 1 
    AND A.D_TRADE = '2024-01-01'  
ORDER BY D_TRADE,C_TD_NO,C_TD_CHAN_CODE,C_SH_ACC_CODE ASC;
