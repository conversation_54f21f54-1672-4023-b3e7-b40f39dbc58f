/**
 * 测试修复后的代码
 */
public class TestFixed {
    public static void main(String[] args) {
        System.out.println("=== 测试修复后的SQL分析器 ===");
        
        // 创建分析结果对象
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // 模拟你的复杂SQL分析结果
        System.out.println("模拟分析复杂SQL语句的结果...");
        
        // 第一个查询：主查询中的第一个UNION部分
        SqlAnalysisResult.QueryInfo query1 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT... FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE..."
        );
        query1.addTable("T_D_AI_STOCK");
        query1.addTable("T_S_DAI_ITEM");
        
        // 表关联字段
        query1.addJoinColumn("A.C_DAI_CODE");
        query1.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤条件字段
        query1.addFilterColumn("a.c_year_month");
        query1.addFilterColumn("A.C_PORT_CODE");
        query1.addFilterColumn("A.D_STOCK");
        query1.addFilterColumn("A.C_DAI_CODE");
        query1.addFilterColumn("A.C_PORT_CLS_CODE");
        query1.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query1);
        
        // 第二个查询：主查询中的第二个UNION部分
        SqlAnalysisResult.QueryInfo query2 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY... FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE..."
        );
        query2.addTable("T_D_AI_ACT_VAL");
        query2.addTable("T_S_DAI_ITEM");
        
        // 表关联字段
        query2.addJoinColumn("A.C_DAI_CODE");
        query2.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤条件字段
        query2.addFilterColumn("A.C_PORT_CODE");
        query2.addFilterColumn("A.D_CHK_ACC");
        query2.addFilterColumn("A.N_CHECK_STATE");
        query2.addFilterColumn("A.C_DAI_CODE");
        query2.addFilterColumn("A.C_PORT_CLS_CODE");
        query2.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query2);
        
        // 第三个查询：主查询中的第三个UNION部分
        SqlAnalysisResult.QueryInfo query3 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY... FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE..."
        );
        query3.addTable("R_D_AI_ACT_VAL");
        query3.addTable("T_S_DAI_ITEM");
        
        // 表关联字段
        query3.addJoinColumn("A.C_DAI_CODE");
        query3.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤条件字段
        query3.addFilterColumn("A.C_PORT_CODE");
        query3.addFilterColumn("A.D_CHK_ACC");
        query3.addFilterColumn("A.N_USEVAT");
        query3.addFilterColumn("A.C_DAI_CODE");
        query3.addFilterColumn("A.C_PORT_CLS_CODE");
        query3.addFilterColumn("A.C_MKT_CODE");
        
        result.addQueryInfo(query3);
        
        // 子查询：NOT IN 子查询
        SqlAnalysisResult.QueryInfo subQuery1 = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER"
        );
        subQuery1.addTable("T_S_VAT_MKTFILTER");
        // 这个子查询没有WHERE条件，所以没有过滤字段和关联字段
        
        result.addQueryInfo(subQuery1);
        
        // 相同的子查询会被分组到一起
        SqlAnalysisResult.QueryInfo subQuery2 = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER (第二次出现)"
        );
        subQuery2.addTable("T_S_VAT_MKTFILTER");
        
        result.addQueryInfo(subQuery2);
        
        SqlAnalysisResult.QueryInfo subQuery3 = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER (第三次出现)"
        );
        subQuery3.addTable("T_S_VAT_MKTFILTER");
        
        result.addQueryInfo(subQuery3);
        
        // 打印分析结果
        result.printResults();
        
        System.out.println("=== 分析说明 ===");
        System.out.println("1. 你的SQL包含1个外层SELECT + 3个UNION ALL的内层SELECT + 3个相同的子查询");
        System.out.println("2. 系统按照 [表+过滤条件+关联字段] 的组合进行分组");
        System.out.println("3. 相同模式的查询会被归为一组，方便统计和优化");
        System.out.println("4. 关联字段是表与表之间连接的字段（如 A.C_DAI_CODE = B.C_DAI_CODE）");
        System.out.println("5. 过滤字段是WHERE条件中用于筛选数据的字段");
        
        System.out.println("\n=== 测试完成 ===");
    }
}
