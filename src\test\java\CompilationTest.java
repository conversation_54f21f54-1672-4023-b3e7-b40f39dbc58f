/**
 * 编译测试 - 验证所有类都能正常编译
 */
public class CompilationTest {
    public static void main(String[] args) {
        System.out.println("=== 编译测试 ===");
        
        try {
            // 测试SqlAnalysisResult
            SqlAnalysisResult result = new SqlAnalysisResult();
            System.out.println("✅ SqlAnalysisResult 编译正常");
            
            // 测试ComprehensiveSqlAnalyzer
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();
            System.out.println("✅ ComprehensiveSqlAnalyzer 编译正常");
            
            // 测试SubQueryFinder
            SubQueryFinder finder = new SubQueryFinder();
            System.out.println("✅ SubQueryFinder 编译正常");
            
            // 测试EnhancedWhereAnalyzer
            java.util.Set<String> tables = new java.util.HashSet<>();
            tables.add("test_table");
            EnhancedWhereAnalyzer whereAnalyzer = new EnhancedWhereAnalyzer(tables);
            System.out.println("✅ EnhancedWhereAnalyzer 编译正常");
            
            // 创建一个简单的测试数据
            SqlAnalysisResult.QueryInfo queryInfo = new SqlAnalysisResult.QueryInfo("SELECT * FROM test");
            queryInfo.addTable("test_table");
            queryInfo.addFilterColumn("id");
            queryInfo.addJoinColumn("foreign_key");
            
            result.addQueryInfo(queryInfo);
            
            System.out.println("\n=== 功能测试 ===");
            result.printResults();
            
            System.out.println("\n✅ 所有类编译和运行正常！");
            System.out.println("🎉 JSqlParser 5.3 兼容性修复成功！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败:");
            e.printStackTrace();
        }
    }
}
