-- 测试SQL文件
-- 第一个查询：简单的两表关联
SELECT a.id, a.name, b.amount 
FROM users a, orders b 
WHERE a.id = b.user_id AND a.status = 'active';

-- 第二个查询：包含子查询
SELECT * FROM products 
WHERE category_id IN (
    SELECT id FROM categories 
    WHERE active = 1 AND parent_id IS NOT NULL
);

-- 第三个查询：多表JOIN
SELECT u.name, p.title, o.amount
FROM users u
JOIN profiles p ON u.id = p.user_id
JOIN orders o ON u.id = o.user_id
WHERE u.created_date > '2024-01-01' AND o.status = 'completed';
