import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

/**
 * GroupKey聚合功能测试
 * 验证新的分组键（表名+过滤条件字段）和关联字段集合合并功能
 */
public class GroupKeyAggregationTest {
    
    public static void main(String[] args) {
        System.out.println("=== GroupKey聚合功能测试 ===");
        System.out.println("测试目标：验证表名+过滤条件字段分组，关联字段集合合并");
        System.out.println();
        
        try {
            // 测试1：相同表+相同过滤条件，不同关联字段应该聚合
            testSameTableSameFilter();
            
            // 测试2：相同表+不同过滤条件，应该分为不同组
            testSameTableDifferentFilter();
            
            // 测试3：不同表+相同过滤条件，应该分为不同组
            testDifferentTableSameFilter();
            
            System.out.println("🎉 GroupKey聚合功能测试完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误:");
            e.printStackTrace();
        }
    }
    
    /**
     * 测试相同表+相同过滤条件，不同关联字段应该聚合
     */
    private static void testSameTableSameFilter() throws Exception {
        System.out.println("=== 测试1：相同表+相同过滤条件，不同关联字段聚合 ===");
        
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // SQL1: users表，status过滤，关联orders表
        String sql1 = "SELECT u.id, u.name, o.amount FROM users u, orders o WHERE u.id = o.user_id AND u.status = 'active'";
        addSqlToResult(result, sql1, "SQL1");
        
        // SQL2: users表，相同的status过滤，关联profiles表
        String sql2 = "SELECT u.id, u.email, p.title FROM users u, profiles p WHERE u.id = p.user_id AND u.status = 'active'";
        addSqlToResult(result, sql2, "SQL2");
        
        System.out.println("预期结果：应该聚合为1个组，关联字段合并为[id, user_id]");
        System.out.println();
        
        // 打印结果
        result.printResults();
        
        // 验证聚合结果
        var aggregatedResults = result.getAggregatedResults();
        System.out.println("实际分组数量: " + aggregatedResults.size());
        
        for (var entry : aggregatedResults.entrySet()) {
            var key = entry.getKey();
            var info = entry.getValue();
            System.out.println("分组键: " + key);
            System.out.println("聚合信息: " + info);
        }
        
        System.out.println("✅ 测试1完成\n");
    }
    
    /**
     * 测试相同表+不同过滤条件，应该分为不同组
     */
    private static void testSameTableDifferentFilter() throws Exception {
        System.out.println("=== 测试2：相同表+不同过滤条件，分为不同组 ===");
        
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // SQL1: users表，status过滤
        String sql1 = "SELECT u.id, u.name FROM users u WHERE u.status = 'active'";
        addSqlToResult(result, sql1, "SQL1");
        
        // SQL2: users表，不同的created_date过滤
        String sql2 = "SELECT u.id, u.email FROM users u WHERE u.created_date > '2024-01-01'";
        addSqlToResult(result, sql2, "SQL2");
        
        System.out.println("预期结果：应该分为2个不同的组");
        System.out.println();
        
        // 打印结果
        result.printResults();
        
        // 验证聚合结果
        var aggregatedResults = result.getAggregatedResults();
        System.out.println("实际分组数量: " + aggregatedResults.size());
        
        System.out.println("✅ 测试2完成\n");
    }
    
    /**
     * 测试不同表+相同过滤条件，应该分为不同组
     */
    private static void testDifferentTableSameFilter() throws Exception {
        System.out.println("=== 测试3：不同表+相同过滤条件，分为不同组 ===");
        
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // SQL1: users表，status过滤
        String sql1 = "SELECT u.id, u.name FROM users u WHERE u.status = 'active'";
        addSqlToResult(result, sql1, "SQL1");
        
        // SQL2: orders表，相同的status过滤
        String sql2 = "SELECT o.id, o.amount FROM orders o WHERE o.status = 'active'";
        addSqlToResult(result, sql2, "SQL2");
        
        System.out.println("预期结果：应该分为2个不同的组（不同表）");
        System.out.println();
        
        // 打印结果
        result.printResults();
        
        // 验证聚合结果
        var aggregatedResults = result.getAggregatedResults();
        System.out.println("实际分组数量: " + aggregatedResults.size());
        
        System.out.println("✅ 测试3完成\n");
    }
    
    /**
     * 辅助方法：将SQL添加到结果中
     */
    private static void addSqlToResult(SqlAnalysisResult result, String sql, String sqlName) throws Exception {
        System.out.println(sqlName + ": " + (sql.length() > 80 ? sql.substring(0, 80) + "..." : sql));
        
        Statement statement = CCJSqlParserUtil.parse(sql);
        
        if (statement instanceof Select) {
            Select select = (Select) statement;
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();
            select.accept(analyzer, null);
            
            SqlAnalysisResult tempResult = analyzer.getAnalysisResult();
            for (SqlAnalysisResult.QueryInfo queryInfo : tempResult.getAllQueries()) {
                queryInfo.setSqlFragment(sqlName + ": " + queryInfo.getSqlFragment());
                result.addQueryInfo(queryInfo);
            }
        }
    }
}
