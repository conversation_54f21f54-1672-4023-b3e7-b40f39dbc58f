import java.io.File;

/**
 * SQL文件分析器主程序
 * 用于批量分析包含多个SQL语句的文件
 */
public class SqlFileAnalyzer {
    public static void main(String[] args) {
        System.out.println("=== SQL文件分析器 ===");
        System.out.println("功能：批量分析文件中的多个SELECT语句");
        System.out.println("输出：按表组织的字段分析结果");
        System.out.println();
        
        try {
            String sqlFilePath;
            
            // 检查命令行参数
            if (args.length > 0) {
                sqlFilePath = args[0];
                System.out.println("使用命令行指定的文件: " + sqlFilePath);
            } else {
                // 使用测试资源文件
                sqlFilePath = "src/test/resources/example.sql";
                System.out.println("未指定文件，使用测试文件: " + sqlFilePath);
                
                // 检查测试文件是否存在，不存在则使用默认示例
                File file = new File(sqlFilePath);
                if (!file.exists()) {
                    System.out.println("测试文件不存在，使用默认示例文件...");
                    sqlFilePath = "sample_sqls.txt";
                    file = new File(sqlFilePath);
                    if (!file.exists()) {
                        System.out.println("创建示例文件...");
                        SqlFileProcessor.createSampleSqlFile(sqlFilePath);
                        System.out.println();
                    }
                }
            }
            
            // 检查文件是否存在
            File file = new File(sqlFilePath);
            if (!file.exists()) {
                System.err.println("❌ 文件不存在: " + sqlFilePath);
                System.out.println("\n使用方法:");
                System.out.println("1. java SqlFileAnalyzer [文件路径]");
                System.out.println("2. 或者将SQL文件命名为 sample_sqls.txt 放在当前目录");
                System.out.println("\n文件格式要求:");
                System.out.println("- 每个SQL语句以分号(;)结尾");
                System.out.println("- 每个SQL语句后换行");
                System.out.println("- 支持多行SQL和注释");
                return;
            }
            
            // 处理SQL文件
            TableFieldAnalysisResult result = SqlFileProcessor.processSqlFile(sqlFilePath);

            // 转换为聚合结果
            AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(result);

            // 打印CSV格式结果
            aggregatedResult.printCsvResults();

            // 保存CSV文件
            String csvFileName = "sql_analysis_result.csv";
            aggregatedResult.saveCsvToFile(csvFileName);

            System.out.println("\n=== 详细统计信息 ===");
            aggregatedResult.printResults();
            
            // 打印使用说明
            System.out.println("\n=== 结果说明 ===");
            System.out.println("📊 关联字段：表与表之间连接的字段（如 a.id = b.user_id）");
            System.out.println("🔍 过滤字段：WHERE条件中用于筛选的字段（如 status = 'active'）");
            System.out.println("📋 所有字段：该表在所有SQL中涉及的全部字段");
            System.out.println();
            System.out.println("💡 提示：如果需要分析其他SQL文件，请使用：");
            System.out.println("   mvn exec:java -Dexec.mainClass=SqlFileAnalyzer -Dexec.args=\"your_file.sql\"");
            
        } catch (Exception e) {
            System.err.println("❌ 处理过程中出现错误:");
            e.printStackTrace();
            
            System.out.println("\n🔧 常见问题解决:");
            System.out.println("1. 检查文件编码是否为UTF-8");
            System.out.println("2. 检查SQL语法是否正确");
            System.out.println("3. 确保每个SQL以分号结尾");
            System.out.println("4. 检查文件路径是否正确");
        }
    }
}
