# 测试目录结构说明

## 整理原则
按照一个Java类对应一个测试类的原则进行整理，删除重复和冗余的测试文件。

## 测试类对应关系

### 主要功能类测试
| 主类 | 测试类 | 说明 |
|------|--------|------|
| `SqlFileBatchAnalyzer` | `SqlFileBatchAnalyzerTest` | 主程序功能测试，整合了原来的多个测试 |
| `AggregatedTableFieldResult` | `AggregationTest` | 聚合功能测试 |
| `TableFieldAnalysisResult` | `TableFieldAnalysisTest` | 表字段分析结果测试 |

### 功能特性测试
| 功能 | 测试类 | 说明 |
|------|--------|------|
| CSV输出 | `CsvOutputTest` | CSV格式输出功能测试 |
| 编译兼容性 | `CompilationTest` | JSqlParser 5.3兼容性测试 |
| API兼容性 | `ApiCompatibilityTest` | API接口兼容性测试 |
| 字段名清理 | `FieldNameCleaningTest` | 验证字段名清理功能 |
| JUnit测试 | `AppTest` | JUnit框架测试模板 |

### 测试运行器
| 文件 | 说明 |
|------|------|
| `AllTestRunner` | 统一的测试运行器，按顺序执行所有测试 |

## 测试资源文件

### src/test/resources/
| 文件 | 说明 |
|------|------|
| `basic_test.sql` | 基础SQL测试文件（原test.sql） |
| `integration_test.sql` | 整合测试文件（原test_integration.sql） |
| `complex_business_test.sql` | 复杂业务SQL测试文件（原complex_test.sql） |
| `field_cleaning_test.sql` | 字段名清理功能测试SQL |
| `aggregation_test.sql` | 聚合功能测试SQL |
| `example.sql` | 示例SQL文件 |
| `sample_sqls.txt` | 示例SQL集合 |
| `test_sqls.txt` | 测试SQL集合 |

## 已删除的重复文件
以下文件已被删除，功能已整合到对应的测试类中：
- `BasicTest.java` → 整合到 `SqlFileBatchAnalyzerTest`
- `SimpleTest.java` → 整合到 `SqlFileBatchAnalyzerTest`
- `FinalTest.java` → 功能重复，已删除
- `FinalDemo.java` → 功能重复，已删除
- `DemoApp.java` → 功能重复，已删除
- `FileFormatDemo.java` → 功能重复，已删除
- `TestFixed.java` → 功能重复，已删除
- `ComprehensiveSqlAnalyzerTest.java` → 功能整合到其他测试中

## 运行测试

### 运行所有测试
```bash
java AllTestRunner
```

### 运行单个测试
```bash
java SqlFileBatchAnalyzerTest
java AggregationTest
java TableFieldAnalysisTest
java CsvOutputTest
java CompilationTest
java ApiCompatibilityTest
java FieldNameCleaningTest
```

### 使用Maven运行测试
```bash
mvn test
```

## 测试覆盖范围

### SqlFileBatchAnalyzer测试
- ✅ 基础SQL文件处理
- ✅ 复杂业务SQL分析
- ✅ 聚合功能验证
- ✅ CSV输出功能
- ✅ 命令行参数处理

### 核心功能测试
- ✅ 多表关联分析
- ✅ WHERE条件解析
- ✅ JOIN语法支持
- ✅ 子查询处理
- ✅ CASE WHEN表达式
- ✅ 函数调用识别

### 输出格式测试
- ✅ 控制台输出格式
- ✅ CSV文件输出
- ✅ 按表组织的结果
- ✅ 聚合统计结果

### 兼容性测试
- ✅ JSqlParser 5.3兼容性
- ✅ Java 8兼容性
- ✅ Maven构建兼容性
- ✅ JUnit 5兼容性

## 注意事项
1. 所有测试文件都使用UTF-8编码
2. SQL文件以分号结尾，支持多行和注释
3. 测试资源文件统一放在`src/test/resources`目录下
4. 每个测试类都有详细的输出说明和错误处理
