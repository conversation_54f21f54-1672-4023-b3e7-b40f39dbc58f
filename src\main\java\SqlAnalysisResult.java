import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 存储SQL分析结果的数据结构
 * 按表、过滤条件、关联字段三个维度分组
 */
public class SqlAnalysisResult {
    
    /**
     * 单个SQL查询的分析结果
     */
    public static class QueryInfo {
        private Set<String> tables = new HashSet<>();           // 涉及的表
        private Set<String> filterColumns = new HashSet<>();    // WHERE条件中的过滤字段
        private Set<String> joinColumns = new HashSet<>();      // 表关联字段
        private String sqlFragment;                             // SQL片段（用于调试）
        
        public QueryInfo(String sqlFragment) {
            this.sqlFragment = sqlFragment;
        }
        
        // Getters and Setters
        public Set<String> getTables() { return tables; }
        public void setTables(Set<String> tables) { this.tables = tables; }
        public void addTable(String table) { this.tables.add(table); }
        
        public Set<String> getFilterColumns() { return filterColumns; }
        public void setFilterColumns(Set<String> filterColumns) { this.filterColumns = filterColumns; }
        public void addFilterColumn(String column) {
            this.filterColumns.add(cleanFieldName(column));
        }

        public Set<String> getJoinColumns() { return joinColumns; }
        public void setJoinColumns(Set<String> joinColumns) { this.joinColumns = joinColumns; }
        public void addJoinColumn(String column) {
            this.joinColumns.add(cleanFieldName(column));
        }
        
        public String getSqlFragment() { return sqlFragment; }
        public void setSqlFragment(String sqlFragment) { this.sqlFragment = sqlFragment; }

        /**
         * 清理字段名：去掉表别名和"."
         * 例如：a.user_id -> user_id, users.name -> name
         */
        private String cleanFieldName(String field) {
            if (field != null && field.contains(".")) {
                return field.substring(field.lastIndexOf(".") + 1);
            }
            return field;
        }

        @Override
        public String toString() {
            return String.format("QueryInfo{tables=%s, filterColumns=%s, joinColumns=%s}", 
                               tables, filterColumns, joinColumns);
        }
    }
    
    /**
     * 分组键：表+过滤条件+关联字段的组合
     */
    public static class GroupKey {
        private final Set<String> tables;
        private final Set<String> filterColumns;
        private final Set<String> joinColumns;
        
        public GroupKey(Set<String> tables, Set<String> filterColumns, Set<String> joinColumns) {
            this.tables = new HashSet<>(tables);
            this.filterColumns = new HashSet<>(filterColumns);
            this.joinColumns = new HashSet<>(joinColumns);
        }
        
        public Set<String> getTables() { return tables; }
        public Set<String> getFilterColumns() { return filterColumns; }
        public Set<String> getJoinColumns() { return joinColumns; }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            GroupKey groupKey = (GroupKey) o;
            return Objects.equals(tables, groupKey.tables) &&
                   Objects.equals(filterColumns, groupKey.filterColumns) &&
                   Objects.equals(joinColumns, groupKey.joinColumns);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(tables, filterColumns, joinColumns);
        }
        
        @Override
        public String toString() {
            return String.format("GroupKey{tables=%s, filterColumns=%s, joinColumns=%s}", 
                               tables, filterColumns, joinColumns);
        }
    }
    
    // 存储所有查询信息
    private List<QueryInfo> allQueries = new ArrayList<>();
    
    // 按分组键存储查询信息
    private Map<GroupKey, List<QueryInfo>> groupedResults = new HashMap<>();
    
    /**
     * 添加一个查询信息
     */
    public void addQueryInfo(QueryInfo queryInfo) {
        allQueries.add(queryInfo);
        
        // 创建分组键并添加到分组结果中
        GroupKey key = new GroupKey(queryInfo.getTables(), 
                                   queryInfo.getFilterColumns(), 
                                   queryInfo.getJoinColumns());
        
        groupedResults.computeIfAbsent(key, k -> new ArrayList<>()).add(queryInfo);
    }
    
    /**
     * 获取所有查询信息
     */
    public List<QueryInfo> getAllQueries() {
        return allQueries;
    }
    
    /**
     * 获取分组后的结果
     */
    public Map<GroupKey, List<QueryInfo>> getGroupedResults() {
        return groupedResults;
    }
    
    /**
     * 打印分析结果
     */
    public void printResults() {
        System.out.println("=== SQL分析结果 ===");
        System.out.println("总共发现 " + allQueries.size() + " 个查询");
        System.out.println("分为 " + groupedResults.size() + " 个不同的组合");
        System.out.println();
        
        int groupIndex = 1;
        for (Map.Entry<GroupKey, List<QueryInfo>> entry : groupedResults.entrySet()) {
            GroupKey key = entry.getKey();
            List<QueryInfo> queries = entry.getValue();
            
            System.out.println("=== 组合 " + groupIndex + " ===");
            System.out.println("涉及表: " + key.getTables());
            System.out.println("过滤字段: " + key.getFilterColumns());
            System.out.println("关联字段: " + key.getJoinColumns());
            System.out.println("查询数量: " + queries.size());
            
            for (int i = 0; i < queries.size(); i++) {
                QueryInfo query = queries.get(i);
                System.out.println("  查询 " + (i + 1) + ": " + 
                                 (query.getSqlFragment().length() > 100 ? 
                                  query.getSqlFragment().substring(0, 100) + "..." : 
                                  query.getSqlFragment()));
            }
            System.out.println();
            groupIndex++;
        }
    }
}
