-- 聚合功能测试SQL文件
-- 包含重复的表和过滤字段组合，用于测试聚合功能

-- SQL 1: users表，status过滤
select u.id, u.name 
from users u 
where u.status = 'active';

-- SQL 2: users表，相同的status过滤（应该被聚合）
SELECT u.user_id, u.email
FROM users u
WHERE u.status = 'active';

-- SQL 3: users表，status + created_date过滤
select u.id, u.name
from users u
where u.status = 'active' and u.created_date > '2024-01-01';

-- SQL 4: users表，相同的status + created_date过滤（应该被聚合）
SELECT u.username
FROM users u
WHERE u.created_date > '2024-01-01' AND u.status = 'active';

-- SQL 5: orders表，关联users
select o.order_id, u.name
from orders o, users u
where o.user_id = u.id and o.status = 'completed';

-- SQL 6: orders表，相同的关联和过滤（应该被聚合）
SELECT o.total_amount, u.email
FROM orders o, users u
WHERE o.user_id = u.id AND o.status = 'completed';

-- SQL 7: products表，多个过滤条件，测试按出现次数排序
select * from products where category_id = 1;

-- SQL 8: products表，category_id出现第二次
select * from products where category_id = 2 and price > 100;

-- SQL 9: products表，category_id出现第三次（应该排在前面）
select * from products where active = 1 and category_id = 3;
