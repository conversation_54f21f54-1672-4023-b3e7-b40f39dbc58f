-- 测试SQL文件
-- 模拟复杂的业务查询

-- 查询1：用户订单统计
SELECT u.user_id, u.username, COUNT(o.order_id) as order_count
FROM users u
LEFT JOIN orders o ON u.user_id = o.user_id
WHERE u.status = 'active' AND u.created_date >= '2024-01-01'
GROUP BY u.user_id, u.username;

-- 查询2：产品销售分析
SELECT p.product_id, p.product_name, SUM(oi.quantity) as total_sold
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
JOIN orders o ON oi.order_id = o.order_id
WHERE o.order_date >= '2024-01-01' AND o.status = 'completed'
GROUP BY p.product_id, p.product_name;

-- 查询3：复杂的嵌套查询
SELECT *
FROM customers c
WHERE c.customer_id IN (
    SELECT DISTINCT o.customer_id
    FROM orders o
    WHERE o.total_amount > (
        SELECT AVG(total_amount)
        FROM orders
        WHERE order_date >= '2024-01-01'
    )
);

-- 查询4：UNION查询示例
SELECT 'high_value' as customer_type, customer_id, total_spent
FROM (
    SELECT customer_id, SUM(total_amount) as total_spent
    FROM orders
    WHERE order_date >= '2024-01-01'
    GROUP BY customer_id
    HAVING SUM(total_amount) > 1000
) high_customers
UNION ALL
SELECT 'regular' as customer_type, customer_id, total_spent
FROM (
    SELECT customer_id, SUM(total_amount) as total_spent
    FROM orders
    WHERE order_date >= '2024-01-01'
    GROUP BY customer_id
    HAVING SUM(total_amount) <= 1000
) regular_customers;
