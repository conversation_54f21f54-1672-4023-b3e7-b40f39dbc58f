-- 整合测试SQL文件
-- 用于验证SqlFileBatchAnalyzer的功能

-- 查询1：简单关联
SELECT u.id, u.name, o.amount
FROM users u, orders o
WHERE u.id = o.user_id AND u.status = 'active';

-- 查询2：JOIN语法
SELECT p.product_name, c.category_name
FROM products p
JOIN categories c ON p.category_id = c.id
WHERE p.price > 100;

-- 查询3：子查询
SELECT * FROM customers
WHERE customer_id IN (
    SELECT customer_id FROM orders
    WHERE order_date >= '2024-01-01'
);
