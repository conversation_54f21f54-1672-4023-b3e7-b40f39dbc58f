/**
 * CSV输出功能测试
 */
public class CsvOutputTest {
    public static void main(String[] args) {
        System.out.println("=== CSV输出功能演示 ===");
        System.out.println("输出格式：表名,过滤字段组,关联字段组,所有字段组");
        System.out.println();
        
        // 创建测试数据
        SqlAnalysisResult sqlResult = new SqlAnalysisResult();
        
        // 模拟一些测试数据
        createTestData(sqlResult);
        
        // 转换为聚合结果
        AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(
            TableFieldAnalysisResult.fromSqlAnalysisResult(sqlResult)
        );
        
        // 输出CSV格式
        aggregatedResult.printCsvResults();
        
        // 保存到文件
        aggregatedResult.saveCsvToFile("test_output.csv");
        
        System.out.println("\n=== CSV格式说明 ===");
        System.out.println("✅ 第1列：表名 - 数据库表的名称");
        System.out.println("✅ 第2列：过滤字段组 - WHERE条件中的筛选字段，用逗号分隔");
        System.out.println("✅ 第3列：关联字段组 - 表间连接的字段，用逗号分隔");
        System.out.println("✅ 第4列：所有字段组 - 该表涉及的全部字段，用逗号分隔");
        System.out.println("✅ 字段已去掉表别名前缀，全部转为大写");
        System.out.println("✅ 过滤字段按出现次数排序（出现多的排前面）");
        System.out.println("✅ 可直接导入Excel或其他数据分析工具");
    }
    
    private static void createTestData(SqlAnalysisResult sqlResult) {
        // 用户表查询1
        SqlAnalysisResult.QueryInfo userQuery1 = new SqlAnalysisResult.QueryInfo(
            "SELECT u.id, u.name FROM users u WHERE u.status = 'active'"
        );
        userQuery1.addTable("USERS");
        userQuery1.addFilterColumn("STATUS");
        sqlResult.addQueryInfo(userQuery1);
        
        // 用户表查询2 - 相同过滤条件，会被聚合
        SqlAnalysisResult.QueryInfo userQuery2 = new SqlAnalysisResult.QueryInfo(
            "SELECT u.email FROM users u WHERE u.status = 'active'"
        );
        userQuery2.addTable("USERS");
        userQuery2.addFilterColumn("STATUS");
        sqlResult.addQueryInfo(userQuery2);
        
        // 订单表查询 - 包含关联字段
        SqlAnalysisResult.QueryInfo orderQuery = new SqlAnalysisResult.QueryInfo(
            "SELECT o.id, u.name FROM orders o, users u WHERE o.user_id = u.id AND o.status = 'completed'"
        );
        orderQuery.addTable("ORDERS");
        orderQuery.addTable("USERS");
        orderQuery.addJoinColumn("USER_ID");
        orderQuery.addJoinColumn("ID");
        orderQuery.addFilterColumn("STATUS");
        sqlResult.addQueryInfo(orderQuery);
        
        // 产品表查询 - 多个过滤条件
        SqlAnalysisResult.QueryInfo productQuery = new SqlAnalysisResult.QueryInfo(
            "SELECT * FROM products WHERE category_id = 1 AND price > 100 AND active = 1"
        );
        productQuery.addTable("PRODUCTS");
        productQuery.addFilterColumn("CATEGORY_ID");
        productQuery.addFilterColumn("PRICE");
        productQuery.addFilterColumn("ACTIVE");
        sqlResult.addQueryInfo(productQuery);
        
        System.out.println("已创建 " + sqlResult.getAllQueries().size() + " 个测试查询");
        System.out.println();
    }
}
