import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
public class App {
    public static void main(String[] args) {
        System.out.println("开始SQL分析...");
        try {
            // 测试用的复杂SQL语句
            String sqlStr = "SELECT SUM(N_AMOUNT) AS N_AMOUNT,SUM(N_ORIG_MONEY) AS N_ORIG_MONEY,SUM(N_PORT_MONEY) AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE\r\n" + //
                                "FROM (  SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVE\r\n" + //
                                "ST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE  FROM T_D_AI_STOCK A,T_S_DAI_ITEM B  WHERE a.c_year_month = $1 and A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_DAI_CODE <> ?\r\n" + //
                                "AND A.C_PORT_CLS_CODE = $4  AND A.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ? \r\n" + //
                                "UNION ALL SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY *\r\n" + //
                                "A.N_WAY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE  FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $5 AND (A.D_CHK_ACC > $6 AND A.D_CHK_ACC <= $7) AND A.N_CHECK_STATE = ? AND A.C_DAI_CODE <> ?   AND A.C_PORT_CLS_CODE\r\n" + //
                                "= $8  AND A.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ? UNION ALL  SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * A.N_WAY * B.N_FUND_WAY\r\n" + //
                                " AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE  FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $9 AND A.D_CHK_ACC > $10 AND A.D_CHK_ACC <= $11  AND A.C_DAI_CODE <> ?  AND A.N_USEVAT <> ?   AND A.C_PORT_CLS_CODE = $12  AND A.C_MKT_CODE\r\n" + //
                                "NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ?) A GROUP BY A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE";

            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(sqlStr);

            // 使用新的综合分析器
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();

            if (statement instanceof Select) {
                Select select = (Select) statement;
                select.accept(analyzer, null);

                // 获取原始分析结果
                SqlAnalysisResult result = analyzer.getAnalysisResult();

                // 转换为按表组织的结果格式
                TableFieldAnalysisResult tableResult = TableFieldAnalysisResult.fromSqlAnalysisResult(result);

                // 打印按表组织的结果
                tableResult.printResults();

                System.out.println("\n=== 原始分析结果（可选查看） ===");
                result.printResults();

            } else {
                System.out.println("不是SELECT语句，无法分析");
            }

        } catch (Exception e) {
            System.err.println("SQL分析过程中出现错误:");
            e.printStackTrace();
        }
    }
}