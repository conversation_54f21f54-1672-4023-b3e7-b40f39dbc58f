import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

/**
 * SQL文件分析器主程序
 * 整合了SqlFileAnalyzer和SqlFileProcessor的功能
 * 支持命令行参数指定要分析的SQL文件
 */
public class App {
    public static void main(String[] args) {
        System.out.println("=== SQL文件分析器 ===");
        System.out.println("功能：批量分析文件中的多个SELECT语句");
        System.out.println("输出：按表组织的字段分析结果");
        System.out.println();

        try {
            String sqlFilePath;

            // 检查命令行参数
            if (args.length > 0) {
                sqlFilePath = args[0];
                System.out.println("使用命令行指定的文件: " + sqlFilePath);
            } else {
                // 使用测试资源文件
                sqlFilePath = "src/test/resources/example.sql";
                System.out.println("未指定文件，使用测试文件: " + sqlFilePath);

                // 检查测试文件是否存在，不存在则使用默认示例
                File file = new File(sqlFilePath);
                if (!file.exists()) {
                    System.out.println("测试文件不存在，使用默认示例文件...");
                    sqlFilePath = "sample_sqls.txt";
                    file = new File(sqlFilePath);
                    if (!file.exists()) {
                        System.out.println("创建示例文件...");
                        createSampleSqlFile(sqlFilePath);
                        System.out.println();
                    }
                }
            }

            // 检查文件是否存在
            File file = new File(sqlFilePath);
            if (!file.exists()) {
                System.err.println("❌ 文件不存在: " + sqlFilePath);
                System.out.println("\n使用方法:");
                System.out.println("1. java App [文件路径]");
                System.out.println("2. 或者将SQL文件命名为 sample_sqls.txt 放在当前目录");
                System.out.println("\n文件格式要求:");
                System.out.println("- 每个SQL语句以分号(;)结尾");
                System.out.println("- 每个SQL语句后换行");
                System.out.println("- 支持多行SQL和注释");
                return;
            }

            // 处理SQL文件
            TableFieldAnalysisResult result = processSqlFile(sqlFilePath);

            // 转换为聚合结果
            AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(result);

            // 打印CSV格式结果
            aggregatedResult.printCsvResults();

            // 保存CSV文件
            String csvFileName = "sql_analysis_result.csv";
            aggregatedResult.saveCsvToFile(csvFileName);

            System.out.println("\n=== 详细统计信息 ===");
            aggregatedResult.printResults();

            // 打印使用说明
            System.out.println("\n=== 结果说明 ===");
            System.out.println("📊 关联字段：表与表之间连接的字段（如 a.id = b.user_id）");
            System.out.println("🔍 过滤字段：WHERE条件中用于筛选的字段（如 status = 'active'）");
            System.out.println("📋 所有字段：该表在所有SQL中涉及的全部字段");
            System.out.println();
            System.out.println("💡 提示：如果需要分析其他SQL文件，请使用：");
            System.out.println("   java App your_file.sql");

        } catch (Exception e) {
            System.err.println("❌ 处理过程中出现错误:");
            e.printStackTrace();

            System.out.println("\n🔧 常见问题解决:");
            System.out.println("1. 检查文件编码是否为UTF-8");
            System.out.println("2. 检查SQL语法是否正确");
            System.out.println("3. 确保每个SQL以分号结尾");
            System.out.println("4. 检查文件路径是否正确");
        }
    }

    /**
     * 处理SQL文件并返回综合分析结果
     */
    public static TableFieldAnalysisResult processSqlFile(String filePath) throws IOException {
        System.out.println("=== 开始处理SQL文件 ===");
        System.out.println("文件路径: " + filePath);

        // 读取文件内容
        String fileContent = new String(Files.readAllBytes(Paths.get(filePath)), "UTF-8");

        // 按分号分割SQL语句
        String[] sqlStatements = splitSqlStatements(fileContent);

        System.out.println("发现 " + sqlStatements.length + " 个SQL语句");
        System.out.println();

        // 创建综合分析结果
        SqlAnalysisResult combinedResult = new SqlAnalysisResult();

        int successCount = 0;
        int errorCount = 0;

        // 逐个分析SQL语句
        for (int i = 0; i < sqlStatements.length; i++) {
            String sql = sqlStatements[i].trim();
            if (sql.isEmpty()) {
                continue;
            }

            System.out.println("分析第 " + (i + 1) + " 个SQL...");

            try {
                // 先将SQL转为大写再解析
                String upperCaseSql = sql.toUpperCase();

                // 解析SQL语句
                Statement statement = CCJSqlParserUtil.parse(upperCaseSql);

                if (statement instanceof Select) {
                    Select select = (Select) statement;

                    // 创建新的分析器实例来分析当前SQL
                    ComprehensiveSqlAnalyzer currentAnalyzer = new ComprehensiveSqlAnalyzer();
                    select.accept(currentAnalyzer, null);

                    // 将当前SQL的分析结果合并到综合结果中
                    SqlAnalysisResult currentResult = currentAnalyzer.getAnalysisResult();
                    for (SqlAnalysisResult.QueryInfo queryInfo : currentResult.getAllQueries()) {
                        // 为每个查询信息添加来源标识
                        String originalSql = queryInfo.getSqlFragment();
                        queryInfo.setSqlFragment("SQL#" + (i + 1) + ": " +
                                               (originalSql.length() > 100 ?
                                                originalSql.substring(0, 100) + "..." :
                                                originalSql));
                        combinedResult.addQueryInfo(queryInfo);
                    }

                    successCount++;
                    System.out.println("✅ 成功分析");
                } else {
                    System.out.println("⚠️  跳过非SELECT语句");
                }

            } catch (Exception e) {
                errorCount++;
                System.err.println("❌ 解析失败: " + e.getMessage());
                System.err.println("SQL内容: " + (sql.length() > 200 ? sql.substring(0, 200) + "..." : sql));
            }
        }

        System.out.println();
        System.out.println("=== 文件处理完成 ===");
        System.out.println("成功分析: " + successCount + " 个SQL");
        System.out.println("失败: " + errorCount + " 个SQL");
        System.out.println("总查询数: " + combinedResult.getAllQueries().size());
        System.out.println();

        // 转换为按表组织的结果
        return TableFieldAnalysisResult.fromSqlAnalysisResult(combinedResult);
    }

    /**
     * 按分号分割SQL语句
     * 处理多行SQL和注释
     */
    private static String[] splitSqlStatements(String content) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();

        String[] lines = content.split("\n");

        for (String line : lines) {
            line = line.trim();

            // 跳过空行和注释行
            if (line.isEmpty() || line.startsWith("--") || line.startsWith("/*")) {
                continue;
            }

            // 移除行末注释
            int commentIndex = line.indexOf("--");
            if (commentIndex != -1) {
                line = line.substring(0, commentIndex).trim();
            }

            currentStatement.append(line).append(" ");

            // 如果行以分号结尾，表示一个SQL语句结束
            if (line.endsWith(";")) {
                String sql = currentStatement.toString().trim();
                if (sql.endsWith(";")) {
                    sql = sql.substring(0, sql.length() - 1).trim(); // 移除分号
                }
                if (!sql.isEmpty()) {
                    statements.add(sql);
                }
                currentStatement = new StringBuilder();
            }
        }

        // 处理最后一个没有分号的SQL
        String lastSql = currentStatement.toString().trim();
        if (!lastSql.isEmpty()) {
            statements.add(lastSql);
        }

        return statements.toArray(new String[0]);
    }

    /**
     * 创建示例SQL文件用于测试
     */
    public static void createSampleSqlFile(String filePath) throws IOException {
        String sampleContent =
            "-- 示例SQL文件\n" +
            "-- 第一个查询：简单的两表关联\n" +
            "SELECT a.id, a.name, b.amount \n" +
            "FROM users a, orders b \n" +
            "WHERE a.id = b.user_id AND a.status = 'active';\n" +
            "\n" +
            "-- 第二个查询：包含子查询\n" +
            "SELECT * FROM products \n" +
            "WHERE category_id IN (\n" +
            "    SELECT id FROM categories \n" +
            "    WHERE active = 1 AND parent_id IS NOT NULL\n" +
            ");\n" +
            "\n" +
            "-- 第三个查询：复杂的UNION查询\n" +
            "SELECT customer_id, amount, 'sale' as type \n" +
            "FROM sales \n" +
            "WHERE date >= '2024-01-01'\n" +
            "UNION ALL\n" +
            "SELECT customer_id, amount, 'refund' as type \n" +
            "FROM refunds \n" +
            "WHERE date >= '2024-01-01';\n" +
            "\n" +
            "-- 第四个查询：多表JOIN\n" +
            "SELECT u.name, p.title, o.amount\n" +
            "FROM users u\n" +
            "JOIN profiles p ON u.id = p.user_id\n" +
            "JOIN orders o ON u.id = o.user_id\n" +
            "WHERE u.created_date > '2024-01-01' AND o.status = 'completed';\n";

        Files.write(Paths.get(filePath), sampleContent.getBytes("UTF-8"));
        System.out.println("已创建示例SQL文件: " + filePath);
    }
}