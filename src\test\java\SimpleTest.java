import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("开始简单测试...");
        
        try {
            // 测试1: 简单的测试SQL
            String simpleSql = "SELECT a.id, b.name FROM table1 a, table2 b WHERE a.id = b.id AND a.status = 'active'";
            System.out.println("=== 测试1: 简单SQL ===");
            System.out.println("解析SQL: " + simpleSql);
            testSql(simpleSql);

            // 测试2: 复杂的业务SQL（简化版）
            String complexSql = "SELECT A.C_SEC_CODE, SEC.C_SEC_VAR_CODE, STOCK.C_DV_ACCOUNT_CODE, " +
                "CASE WHEN A.C_DT_CODE IN ('BUY','SELL') THEN A.C_DT_CODE ELSE 'OTHER' END AS C_DV_TYPE " +
                "FROM T_D_AC_TRADE_IVT A " +
                "LEFT JOIN T_P_SV_SEC_BASE SEC ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = 1 " +
                "LEFT JOIN T_D_MP_PRE_STOCK STOCK ON A.C_SEC_CODE = STOCK.C_SEC_CODE " +
                "WHERE A.C_PORT_CODE = 'PORT001' AND A.C_DT_CODE = 'BUY' AND A.N_CHECK_STATE = 1";

            System.out.println("\n=== 测试2: 复杂业务SQL ===");
            System.out.println("解析SQL: " + (complexSql.length() > 100 ? complexSql.substring(0, 100) + "..." : complexSql));
            testSql(complexSql);

        } catch (Exception e) {
            System.err.println("测试过程中出现错误:");
            e.printStackTrace();
        }
    }

    private static void testSql(String sql) {
        try {
            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(sql);
            System.out.println("✅ SQL解析成功");

            // 使用新的综合分析器
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();

            if (statement instanceof Select) {
                System.out.println("🔬 开始分析SELECT语句...");
                Select select = (Select) statement;
                select.accept(analyzer, null);

                // 获取并打印分析结果
                SqlAnalysisResult result = analyzer.getAnalysisResult();

                System.out.println("📊 分析结果:");
                result.printResults();

                // 转换为按表组织的结果
                TableFieldAnalysisResult tableResult = TableFieldAnalysisResult.fromSqlAnalysisResult(result);
                System.out.println("\n📋 按表组织的结果:");
                tableResult.printResults();

            } else {
                System.out.println("⚠️ 不是SELECT语句，无法分析");
            }

            System.out.println("✅ 测试完成\n");

        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误:");
            e.printStackTrace();
            System.out.println();
        }
    }
}
