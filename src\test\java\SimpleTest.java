import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;

public class SimpleTest {
    public static void main(String[] args) {
        System.out.println("开始简单测试...");
        
        try {
            // 简单的测试SQL
            String simpleSql = "SELECT a.id, b.name FROM table1 a, table2 b WHERE a.id = b.id AND a.status = 'active'";
            
            System.out.println("解析SQL: " + simpleSql);
            
            // 解析SQL语句
            Statement statement = CCJSqlParserUtil.parse(simpleSql);
            System.out.println("SQL解析成功");
            
            // 使用新的综合分析器
            ComprehensiveSqlAnalyzer analyzer = new ComprehensiveSqlAnalyzer();
            
            if (statement instanceof Select) {
                System.out.println("开始分析SELECT语句...");
                Select select = (Select) statement;
                select.accept(analyzer, null);
                
                // 获取并打印分析结果
                SqlAnalysisResult result = analyzer.getAnalysisResult();
                result.printResults();
                
            } else {
                System.out.println("不是SELECT语句，无法分析");
            }
            
            System.out.println("测试完成");
            
        } catch (Exception e) {
            System.err.println("测试过程中出现错误:");
            e.printStackTrace();
        }
    }
}
