public class BasicTest {
    public static void main(String[] args) {
        System.out.println("基本测试开始...");
        
        // 测试SqlAnalysisResult类
        SqlAnalysisResult result = new SqlAnalysisResult();
        
        // 创建一个测试查询信息
        SqlAnalysisResult.QueryInfo queryInfo = new SqlAnalysisResult.QueryInfo("SELECT * FROM test");
        queryInfo.addTable("test_table");
        queryInfo.addFilterColumn("id");
        queryInfo.addJoinColumn("foreign_key");
        
        result.addQueryInfo(queryInfo);
        
        System.out.println("测试数据添加成功");
        result.printResults();
        
        System.out.println("基本测试完成");
    }
}
