SELECT count(a.C_TD_TYPE) as err<PERSON>um,SDI.C_DVA_ITEM_NAME  as errmessage,    cast( string_agg(A.C_SEC_CODE, ? order by <PERSON><PERSON>C_SEC_CODE)  as varchar)  AS SEC  FROM T_D_AC_TRADE_IPO A  LEFT JOIN T_S_DVA_ITEM SDI ON SDI.C_DVA_ITEM_CODE=A.c_Td_Type  WHERE  A.C_TD_TYPE in (?) AND A.C_PORT_CODE = $1  AND A.N_CHECK_STATE = ?  AND A.d_Trade= $2 AND NOT EXISTS(SELECT ? FROM T_P_SV_SEC_BASE B WHERE A.C_SEC_CODE = B.C_SEC_CODE AND B.N_CHECK_STATE = ?)  GROUP BY  C_DVA_ITEM_NAME;
SELECT sec.c_dv_pi_mod as C_DV_PI_MOD, A.C_IDEN AS ID, A.C_SEC_CODE, NVL(SEC.C_SEC_VAR_CODE,?) AS C_SEC_VAR_CODE, A.C_DC_CODE, NVL(SEC.C_MKT_CODE,?) AS C_MKT_CODE, A.C_DV_INVEST_CLS, A.C_DV_ISSUE_MODE, A.C_DTA_CODE, A.C_SETT_MODE,  A.C_TD_CHAN_CODE, A.C_DV_VAR_DUR, A.C_DT_CODE, A.N_TD_AMOUNT, A.N_TD_MONEY, A.N_TD_PRICE, A.N_INCOME,  A.N_SETT_MONEY_FIRST, A.N_SETT_MONEY_DUE,SEC.C_DV_PI_MOD,A.C_DESC, CASE WHEN NVL(A.N_SETT_MONEY_FACT,?) = ? AND NVL(A.N_PAWN_MONEY,?) = ? THEN A.N_SETT_MONEY_DUE ELSE A.N_SETT_MONEY_FACT END AS N_SETT_MONEY_FACT,  A.C_CA_CODE_SETT_DUE AS C_CA_CODE,A.D_TRADE,  A.D_SETT_FIRST, A.D_SETT_DUE,A.D_SETT_FACT,A.N_INTEREST,A.C_TD_NO,A.N_RATE, A.N_TD_MONEY_PORT, STOCK.C_DV_ACCOUNT_CODE,  A.N_PAWN_MONEY, CASE WHEN NVL(SEC.C_SEC_VAR_CODE,?) LIKE ?  THEN NVL(trim(SEC.C_DV_AI_MOD),?)  ELSE ? END AS C_SEC_CODE_TAG,  CASE WHEN A.C_DT_CODE IN (?,?,?)  THEN A.C_DT_CODE ELSE ? END AS C_DV_TYPE_SUB, A.D_EXCHANGE_RATE,NVL(A.C_DEVALUE_STAGE,?) AS C_DEVALUE_STAGE, NVL(A.C_DEVALUE_STAGE,?) AS C_FEE_CODE,  A.C_DV_PLAT,  ? as C_CASH_FLOW_CODE,  CASE WHEN NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? AND NVL(SEC.C_FINANCE_TOOL,?) =?  THEN ?  WHEN (NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? OR NVL(SEC.C_SEC_VAR_CODE,?) LIKE ? OR NVL(SEC.C_SEC_VAR_CODE,?) LIKE ?) AND NVL(SEC.C_FINANCE_TOOL,?) =?  THEN ?  ELSE NVL(SEC.C_FINANCE_TOOL,?) END AS C_FINANCE_TOOL  ,A.N_PAWN_MONEY  ,A.C_SH_ACC_CODE  ,A.C_ORG_CODE  ,A.C_SETT_WAY,A.C_PORT_CODE  ,A.N_MONEY_GZ  ,A.C_PAY_PAT_CODE  ,A.C_SETT_WAY  ,A.N_SETT_MONEY_DUE_FACT  ,A.C_BUSI_TYPE  ,A.N_UNPAID_INT  ,A.C_DV_BCK  ,A.N_COMP_AMT  FROM T_D_AC_TRADE_IVT A LEFT JOIN T_P_SV_SEC_BASE SEC    ON A.C_SEC_CODE = SEC.C_SEC_CODE AND SEC.N_CHECK_STATE = ?  LEFT JOIN T_D_MP_PRE_STOCK STOCK  ON A.C_SEC_CODE = STOCK.C_SEC_CODE and $1 between STOCK.d_ai_begin and STOCK.d_ai_end AND STOCK.N_CHECK_STATE = ?  WHERE A.C_PORT_CODE = $2 AND A.C_DT_CODE = $3 AND A.C_TD_TYPE = $4    AND NVL(A.C_YCDZ_STATE,?) <> ?    AND A.C_DV_INVEST_CLS = $5 AND A.N_CHECK_STATE = ? AND A.D_TRADE = $6  ORDER BY D_TRADE,C_TD_NO,C_TD_CHAN_CODE,C_SH_ACC_CODE ASC;
SELECT SUM(N_AMOUNT) AS N_AMOUNT,SUM(N_ORIG_MONEY) AS N_ORIG_MONEY,SUM(N_PORT_MONEY) AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE FROM (  SELECT A.N_AMOUNT * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE FROM T_D_AI_STOCK A,T_S_DAI_ITEM B  WHERE a.c_year_month = $1 and A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3 AND A.C_DAI_CODE <> ? AND A.C_PORT_CLS_CODE = $4  AND A.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ? UNION ALL SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * A.N_WAY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE  FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $5 AND (A.D_CHK_ACC > $6 AND A.D_CHK_ACC <= $7) AND A.N_CHECK_STATE = ? AND A.C_DAI_CODE <> ?   AND A.C_PORT_CLS_CODE = $8  AND A.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ? UNION ALL  SELECT A.N_AMOUNT * A.N_WAY * B.N_FUND_WAY AS N_AMOUNT,A.N_ORIG_MONEY * A.N_WAY * B.N_FUND_WAY AS N_ORIG_MONEY,A.N_PORT_MONEY * A.N_WAY * B.N_FUND_WAY AS N_PORT_MONEY,A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE  FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B  WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $9 AND A.D_CHK_ACC > $10 AND A.D_CHK_ACC <= $11  AND A.C_DAI_CODE <> ?  AND A.N_USEVAT <> ?   AND A.C_PORT_CLS_CODE = $12  AND A.C_MKT_CODE NOT IN (SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER) AND A.C_DAI_CODE = ?) A GROUP BY A.C_SEC_CODE,A.C_SEC_VAR_CODE,A.C_DTA_CODE,A.C_DV_INVEST_CLS,A.C_DV_ISSUE_MODE,A.C_MKT_CODE;