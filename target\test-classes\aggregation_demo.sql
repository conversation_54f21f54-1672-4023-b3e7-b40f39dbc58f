-- 聚合功能演示SQL文件
-- 测试：去掉表别名、SQL转大写、按表和过滤字段聚合、字段出现次数排序

-- SQL 1: users表，只有status过滤
select u.id, u.name 
from users u 
where u.status = 'active';

-- SQL 2: users表，相同的status过滤（应该被聚合到SQL1）
SELECT u.user_id, u.email
FROM users u
WHERE u.status = 'active';

-- SQL 3: users表，status + created_date过滤
select u.id, u.name
from users u
where u.status = 'active' and u.created_date > '2024-01-01';

-- SQL 4: users表，相同的status + created_date过滤（应该被聚合到SQL3）
SELECT u.username, u.phone
FROM users u
WHERE u.created_date > '2024-01-01' AND u.status = 'active';

-- SQL 5: orders表，关联users，status过滤
select o.order_id, u.name
from orders o, users u
where o.user_id = u.id and o.status = 'completed';

-- SQL 6: orders表，相同的关联和过滤（应该被聚合到SQL5）
SELECT o.total_amount, u.email
FROM orders o, users u
WHERE o.user_id = u.id AND o.status = 'completed';

-- SQL 7: products表，category_id过滤（第1次出现）
select * from products where category_id = 1;

-- SQL 8: products表，category_id + price过滤（category_id第2次出现）
select * from products where category_id = 2 and price > 100;

-- SQL 9: products表，category_id + active过滤（category_id第3次出现，应该排在前面）
select * from products where active = 1 and category_id = 3;

-- SQL 10: products表，只有price过滤
select * from products where price < 50;
