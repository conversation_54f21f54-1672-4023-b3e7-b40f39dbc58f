-- GroupKey聚合功能测试SQL文件
-- 测试表名+过滤条件字段分组，关联字段集合合并

-- 组1：users表 + status过滤条件（应该聚合为一组）
-- SQL1: users表，status过滤，关联orders表
SELECT u.id, u.name, o.amount 
FROM users u, orders o 
WHERE u.id = o.user_id AND u.status = 'active';

-- SQL2: users表，相同的status过滤，关联profiles表
SELECT u.id, u.email, p.title 
FROM users u, profiles p 
WHERE u.id = p.user_id AND u.status = 'active';

-- SQL3: users表，相同的status过滤，关联addresses表
SELECT u.id, u.name, a.address 
FROM users u 
JOIN addresses a ON u.id = a.user_id 
WHERE u.status = 'active';

-- 组2：users表 + created_date过滤条件（不同的过滤条件，应该是新组）
-- SQL4: users表，created_date过滤
SELECT u.id, u.name, o.amount 
FROM users u, orders o 
WHERE u.id = o.user_id AND u.created_date > '2024-01-01';

-- SQL5: users表，相同的created_date过滤，不同关联
SELECT u.id, u.email, p.bio 
FROM users u 
LEFT JOIN profiles p ON u.id = p.user_id 
WHERE u.created_date > '2024-01-01';

-- 组3：orders表 + status过滤条件（不同表，应该是新组）
-- SQL6: orders表，status过滤
SELECT o.id, o.amount, u.name 
FROM orders o, users u 
WHERE o.user_id = u.id AND o.status = 'completed';

-- SQL7: orders表，相同的status过滤，不同关联
SELECT o.id, o.total, p.product_name 
FROM orders o 
JOIN order_items oi ON o.id = oi.order_id 
JOIN products p ON oi.product_id = p.id 
WHERE o.status = 'completed';

-- 组4：复杂多条件过滤（应该是新组）
-- SQL8: users表，多个过滤条件
SELECT u.id, u.name, o.amount 
FROM users u, orders o 
WHERE u.id = o.user_id 
    AND u.status = 'active' 
    AND u.created_date > '2024-01-01' 
    AND u.email_verified = 1;
