/**
 * 测试按表组织的字段分析结果
 */
public class TableFieldAnalysisTest {
    public static void main(String[] args) {
        System.out.println("=== 测试按表组织的字段分析 ===");
        
        // 创建原始分析结果
        SqlAnalysisResult sqlResult = new SqlAnalysisResult();
        
        // 模拟你的复杂SQL的分析结果
        createTestData(sqlResult);
        
        // 转换为按表组织的结果
        TableFieldAnalysisResult tableResult = TableFieldAnalysisResult.fromSqlAnalysisResult(sqlResult);
        
        // 打印结果
        tableResult.printResults();
        
        System.out.println("\n=== 预期输出格式示例 ===");
        System.out.println("T_D_AI_STOCK：关联字段{A.C_DAI_CODE},过滤字段{A.C_PORT_CODE, A.D_STOCK, A.C_MKT_CODE},所有字段{A.C_DAI_CODE, A.C_PORT_CODE, A.D_STOCK, A.C_MKT_CODE}");
        System.out.println("T_S_DAI_ITEM：关联字段{B.C_DAI_CODE},过滤字段{},所有字段{B.C_DAI_CODE}");
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void createTestData(SqlAnalysisResult sqlResult) {
        // 第一个查询：T_D_AI_STOCK A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query1 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_STOCK A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $2 AND A.D_STOCK = $3"
        );
        query1.addTable("T_D_AI_STOCK");
        query1.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query1.addJoinColumn("A.C_DAI_CODE");
        query1.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query1.addFilterColumn("A.C_PORT_CODE");
        query1.addFilterColumn("A.D_STOCK");
        query1.addFilterColumn("A.C_MKT_CODE");
        
        sqlResult.addQueryInfo(query1);
        
        // 第二个查询：T_D_AI_ACT_VAL A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query2 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM T_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE AND A.C_PORT_CODE = $5"
        );
        query2.addTable("T_D_AI_ACT_VAL");
        query2.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query2.addJoinColumn("A.C_DAI_CODE");
        query2.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query2.addFilterColumn("A.C_PORT_CODE");
        query2.addFilterColumn("A.D_CHK_ACC");
        
        sqlResult.addQueryInfo(query2);
        
        // 第三个查询：R_D_AI_ACT_VAL A, T_S_DAI_ITEM B
        SqlAnalysisResult.QueryInfo query3 = new SqlAnalysisResult.QueryInfo(
            "SELECT A.N_AMOUNT * B.N_FUND_WAY FROM R_D_AI_ACT_VAL A,T_S_DAI_ITEM B WHERE A.C_DAI_CODE = B.C_DAI_CODE"
        );
        query3.addTable("R_D_AI_ACT_VAL");
        query3.addTable("T_S_DAI_ITEM");
        
        // 关联字段
        query3.addJoinColumn("A.C_DAI_CODE");
        query3.addJoinColumn("B.C_DAI_CODE");
        
        // 过滤字段
        query3.addFilterColumn("A.N_USEVAT");
        
        sqlResult.addQueryInfo(query3);
        
        // 子查询：T_S_VAT_MKTFILTER
        SqlAnalysisResult.QueryInfo subQuery = new SqlAnalysisResult.QueryInfo(
            "SELECT C_MKT_CODE FROM T_S_VAT_MKTFILTER"
        );
        subQuery.addTable("T_S_VAT_MKTFILTER");
        // 这个子查询没有WHERE条件，所以没有字段
        
        sqlResult.addQueryInfo(subQuery);
        
        System.out.println("已创建 " + sqlResult.getAllQueries().size() + " 个测试查询");
    }
}
