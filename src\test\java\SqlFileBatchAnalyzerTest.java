import java.io.IOException;

/**
 * SqlFileBatchAnalyzer 测试类
 * 整合了所有相关的测试功能
 */
public class SqlFileBatchAnalyzerTest {
    public static void main(String[] args) {
        System.out.println("=== SqlFileBatchAnalyzer 综合测试 ===");
        System.out.println("测试目标：验证SQL文件批量分析功能");
        System.out.println();

        try {
            // 测试1：基础功能测试
            testBasicFunctionality();

            // 测试2：复杂SQL测试
            testComplexSql();

            // 测试3：聚合功能测试
            testAggregationFeature();

            // 测试4：CSV输出测试
            testCsvOutput();

            System.out.println("🎉 所有测试完成！");

        } catch (Exception e) {
            System.err.println("❌ 测试过程中出现错误:");
            e.printStackTrace();
        }
    }

    /**
     * 测试基础功能
     */
    private static void testBasicFunctionality() throws IOException {
        System.out.println("=== 测试1：基础功能 ===");

        // 使用resources中的基础测试文件
        String testFile = "src/test/resources/basic_test.sql";
        TableFieldAnalysisResult result = SqlFileBatchAnalyzer.processSqlFile(testFile);

        System.out.println("✅ 基础功能测试完成");
        System.out.println();
    }

    /**
     * 测试复杂SQL
     */
    private static void testComplexSql() throws IOException {
        System.out.println("=== 测试2：复杂SQL ===");

        String testFile = "src/test/resources/complex_business_test.sql";
        TableFieldAnalysisResult result = SqlFileBatchAnalyzer.processSqlFile(testFile);

        System.out.println("✅ 复杂SQL测试完成");
        System.out.println();
    }

    /**
     * 测试聚合功能
     */
    private static void testAggregationFeature() throws IOException {
        System.out.println("=== 测试3：聚合功能 ===");

        String testFile = "src/test/resources/aggregation_test.sql";
        TableFieldAnalysisResult result = SqlFileBatchAnalyzer.processSqlFile(testFile);

        // 转换为聚合结果
        AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(result);
        aggregatedResult.printResults();

        System.out.println("✅ 聚合功能测试完成");
        System.out.println();
    }

    /**
     * 测试CSV输出
     */
    private static void testCsvOutput() throws IOException {
        System.out.println("=== 测试4：CSV输出 ===");

        String testFile = "src/test/resources/integration_test.sql";
        TableFieldAnalysisResult result = SqlFileBatchAnalyzer.processSqlFile(testFile);

        // 转换为聚合结果并输出CSV
        AggregatedTableFieldResult aggregatedResult = AggregatedTableFieldResult.fromTableFieldAnalysisResult(result);
        aggregatedResult.printCsvResults();
        aggregatedResult.saveCsvToFile("test_output.csv");

        System.out.println("✅ CSV输出测试完成");
        System.out.println();
    }
}
